-- =====================================================
-- 门诊医生站费用计算问题检查与修复脚本
-- 创建日期：2025年8月27日
-- 问题：费用显示计算错误，数学不匹配
-- =====================================================

-- 1. 检查当前费用计算是否存在问题
-- =====================================================
SELECT '=== 费用计算问题检查 ===' AS 检查项目;

-- 1.1 检查COSTS和CHARGES计算是否正确
SELECT 
    PATIENT_ID,
    CLINIC_NO,
    ORDER_NO,
    ORDER_SUB_NO,
    ITEM_NO,
    ITEM_NAME,
    ITEM_PRICE,
    AMOUNT,
    COSTS,
    CHARGES,
    ROUND(ITEM_PRICE * AMOUNT, 4) AS 期望COSTS,
    CASE 
        WHEN PRICE_QUOTIETY > 0 AND PRICE_QUOTIETY != 1 
        THEN ROUND(ITEM_PRICE * AMOUNT * PRICE_QUOTIETY, 4)
        ELSE ROUND(ITEM_PRICE * AMOUNT, 4)
    END AS 期望CHARGES,
    ABS(COSTS - ITEM_PRICE * AMOUNT) AS COSTS差异,
    CASE 
        WHEN PRICE_QUOTIETY > 0 AND PRICE_QUOTIETY != 1 
        THEN ABS(CHARGES - ITEM_PRICE * AMOUNT * PRICE_QUOTIETY)
        ELSE ABS(CHARGES - ITEM_PRICE * AMOUNT)
    END AS CHARGES差异
FROM OUTP_ORDERS_COSTS_STANDARD 
WHERE VISIT_DATE >= TRUNC(SYSDATE) - 7  -- 最近7天的数据
  AND (
    ABS(COSTS - ITEM_PRICE * AMOUNT) > 0.01  -- COSTS计算错误
    OR 
    (PRICE_QUOTIETY > 0 AND PRICE_QUOTIETY != 1 AND ABS(CHARGES - ITEM_PRICE * AMOUNT * PRICE_QUOTIETY) > 0.01)  -- CHARGES计算错误（有系数）
    OR
    (NVL(PRICE_QUOTIETY, 1) = 1 AND ABS(CHARGES - ITEM_PRICE * AMOUNT) > 0.01)  -- CHARGES计算错误（无系数）
  )
ORDER BY VISIT_DATE DESC, PATIENT_ID, ORDER_NO, ORDER_SUB_NO;

-- 1.2 检查是否存在CHARGES = COSTS * AMOUNT的错误模式
SELECT '=== 检查CHARGES = COSTS * AMOUNT错误模式 ===' AS 检查项目;

SELECT 
    PATIENT_ID,
    CLINIC_NO,
    ORDER_NO,
    ORDER_SUB_NO,
    ITEM_NAME,
    ITEM_PRICE,
    AMOUNT,
    COSTS,
    CHARGES,
    ROUND(COSTS * AMOUNT, 4) AS COSTS乘以AMOUNT
FROM OUTP_ORDERS_COSTS_STANDARD 
WHERE VISIT_DATE >= TRUNC(SYSDATE) - 7
  AND ABS(CHARGES - COSTS * AMOUNT) < 0.01  -- 识别CHARGES = COSTS * AMOUNT的错误模式
  AND AMOUNT > 1  -- 排除数量为1的情况
ORDER BY VISIT_DATE DESC, PATIENT_ID, ORDER_NO, ORDER_SUB_NO;

-- 2. 费用汇总检查
-- =====================================================
SELECT '=== 费用汇总检查 ===' AS 检查项目;

-- 2.1 按门诊号汇总费用，检查是否存在异常
SELECT 
    CLINIC_NO,
    COUNT(*) AS 费用项目数,
    SUM(COSTS) AS 总COSTS,
    SUM(CHARGES) AS 总CHARGES,
    SUM(CASE WHEN CHARGE_INDICATOR = 1 THEN COSTS ELSE 0 END) AS 已收COSTS,
    SUM(COSTS) - SUM(CASE WHEN CHARGE_INDICATOR = 1 THEN COSTS ELSE 0 END) AS 未收COSTS,
    CASE 
        WHEN SUM(CHARGES) > SUM(COSTS) * 2 THEN '可能存在重复计算'
        WHEN SUM(CHARGES) < SUM(COSTS) * 0.5 THEN '可能存在计算不足'
        ELSE '正常'
    END AS 状态评估
FROM OUTP_ORDERS_COSTS_STANDARD 
WHERE VISIT_DATE >= TRUNC(SYSDATE) - 3  -- 最近3天
GROUP BY CLINIC_NO
HAVING SUM(CHARGES) > SUM(COSTS) * 1.5 OR SUM(CHARGES) < SUM(COSTS) * 0.8  -- 异常比例
ORDER BY 总CHARGES DESC;

-- 3. 修复脚本（请谨慎执行）
-- =====================================================
-- 注意：以下修复脚本仅在确认问题后执行，建议先备份数据

/*
-- 3.1 修复COSTS计算错误的记录
UPDATE OUTP_ORDERS_COSTS_STANDARD 
SET COSTS = ROUND(ITEM_PRICE * AMOUNT, 4),
    LAST_UPDATE_DATE = SYSDATE
WHERE ABS(COSTS - ITEM_PRICE * AMOUNT) > 0.01
  AND VISIT_DATE >= TRUNC(SYSDATE) - 7;

-- 3.2 修复CHARGES计算错误的记录
UPDATE OUTP_ORDERS_COSTS_STANDARD 
SET CHARGES = CASE 
    WHEN PRICE_QUOTIETY > 0 AND PRICE_QUOTIETY != 1 
    THEN ROUND(ITEM_PRICE * AMOUNT * PRICE_QUOTIETY, 4)
    ELSE ROUND(ITEM_PRICE * AMOUNT, 4)
END,
LAST_UPDATE_DATE = SYSDATE
WHERE (
    (PRICE_QUOTIETY > 0 AND PRICE_QUOTIETY != 1 AND ABS(CHARGES - ITEM_PRICE * AMOUNT * PRICE_QUOTIETY) > 0.01)
    OR
    (NVL(PRICE_QUOTIETY, 1) = 1 AND ABS(CHARGES - ITEM_PRICE * AMOUNT) > 0.01)
)
AND VISIT_DATE >= TRUNC(SYSDATE) - 7;

-- 3.3 同步更新OUTP_ORDERS_STANDARD表的费用汇总
UPDATE OUTP_ORDERS_STANDARD o
SET (COSTS, CHARGES) = (
    SELECT 
        NVL(SUM(c.COSTS), 0),
        NVL(SUM(c.CHARGES), 0)
    FROM OUTP_ORDERS_COSTS_STANDARD c
    WHERE c.CLINIC_NO = o.CLINIC_NO
      AND c.ORDER_NO = o.ORDER_NO
      AND c.ORDER_SUB_NO = o.ORDER_SUB_NO
),
LAST_UPDATE_DATE = SYSDATE
WHERE EXISTS (
    SELECT 1 FROM OUTP_ORDERS_COSTS_STANDARD c
    WHERE c.CLINIC_NO = o.CLINIC_NO
      AND c.ORDER_NO = o.ORDER_NO
      AND c.ORDER_SUB_NO = o.ORDER_SUB_NO
      AND c.VISIT_DATE >= TRUNC(SYSDATE) - 7
);

COMMIT;
*/

-- 4. 验证修复结果
-- =====================================================
SELECT '=== 修复后验证 ===' AS 检查项目;

-- 重新执行第1步的检查查询来验证修复结果
